#!/usr/bin/env python3
"""
Augment VIP - Database Cleaner and Telemetry ID Modifier
A utility toolkit for Augment VIP users, providing tools to manage and clean VS Code databases.
"""

import os
import sys
import sqlite3
import json
import uuid
import secrets
import shutil
import platform
from pathlib import Path
from typing import List, Optional

class Colors:
    """ANSI color codes for terminal output"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_colored(message: str, color: str = Colors.WHITE) -> None:
    """Print colored message to terminal"""
    print(f"{color}{message}{Colors.END}")

def print_header() -> None:
    """Print application header"""
    print_colored("=" * 60, Colors.CYAN)
    print_colored("🚀 Augment VIP - Database Cleaner & ID Modifier", Colors.BOLD + Colors.CYAN)
    print_colored("=" * 60, Colors.CYAN)
    print()

def get_vscode_paths() -> List[Path]:
    """Get VS Code database paths based on operating system"""
    system = platform.system().lower()
    home = Path.home()
    
    paths = []
    
    if system == "windows":
        # Windows paths
        base_paths = [
            home / "AppData" / "Roaming" / "Code" / "User" / "workspaceStorage",
            home / "AppData" / "Roaming" / "Code" / "CachedExtensions",
            home / "AppData" / "Roaming" / "Code" / "logs",
        ]
    elif system == "darwin":  # macOS
        # macOS paths
        base_paths = [
            home / "Library" / "Application Support" / "Code" / "User" / "workspaceStorage",
            home / "Library" / "Application Support" / "Code" / "CachedExtensions",
            home / "Library" / "Application Support" / "Code" / "logs",
        ]
    else:  # Linux and others
        # Linux paths
        base_paths = [
            home / ".config" / "Code" / "User" / "workspaceStorage",
            home / ".config" / "Code" / "CachedExtensions",
            home / ".config" / "Code" / "logs",
        ]
    
    # Find all .db files in these directories
    for base_path in base_paths:
        if base_path.exists():
            for db_file in base_path.rglob("*.db"):
                paths.append(db_file)
    
    return paths

def get_storage_json_path() -> Optional[Path]:
    """Get VS Code storage.json path based on operating system"""
    system = platform.system().lower()
    home = Path.home()
    
    if system == "windows":
        storage_path = home / "AppData" / "Roaming" / "Code" / "User" / "globalStorage" / "storage.json"
    elif system == "darwin":  # macOS
        storage_path = home / "Library" / "Application Support" / "Code" / "User" / "globalStorage" / "storage.json"
    else:  # Linux and others
        storage_path = home / ".config" / "Code" / "User" / "globalStorage" / "storage.json"
    
    return storage_path if storage_path.exists() else None

def create_backup(file_path: Path) -> Path:
    """Create a backup of the file"""
    backup_path = file_path.with_suffix(f"{file_path.suffix}.backup")
    shutil.copy2(file_path, backup_path)
    return backup_path

def clean_database(db_path: Path) -> int:
    """Clean Augment-related entries from a database"""
    try:
        # Create backup
        backup_path = create_backup(db_path)
        print_colored(f"  📁 Created backup: {backup_path.name}", Colors.BLUE)
        
        # Connect to database
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        total_deleted = 0
        
        for table_name in tables:
            table = table_name[0]
            try:
                # Get column names
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                # Look for text columns that might contain 'augment'
                for column in columns:
                    column_name = column[1]
                    column_type = column[2]
                    
                    if 'TEXT' in column_type.upper() or 'VARCHAR' in column_type.upper():
                        # Delete rows containing 'augment' (case insensitive)
                        cursor.execute(f"DELETE FROM {table} WHERE LOWER({column_name}) LIKE '%augment%'")
                        deleted = cursor.rowcount
                        total_deleted += deleted
                        
                        if deleted > 0:
                            print_colored(f"    🗑️  Deleted {deleted} entries from {table}.{column_name}", Colors.YELLOW)
                            
            except sqlite3.Error as e:
                print_colored(f"    ⚠️  Warning: Could not process table {table}: {e}", Colors.YELLOW)
                continue
        
        conn.commit()
        conn.close()
        
        return total_deleted
        
    except Exception as e:
        print_colored(f"  ❌ Error processing {db_path.name}: {e}", Colors.RED)
        return 0

def clean_databases() -> None:
    """Clean all VS Code databases"""
    print_colored("🧹 Starting database cleaning...", Colors.GREEN)
    print()
    
    db_paths = get_vscode_paths()
    
    if not db_paths:
        print_colored("⚠️  No database files found", Colors.YELLOW)
        return
    
    print_colored(f"📊 Found {len(db_paths)} database files", Colors.BLUE)
    print()
    
    total_cleaned = 0
    
    for db_path in db_paths:
        print_colored(f"🔍 Processing: {db_path.name}", Colors.CYAN)
        deleted = clean_database(db_path)
        total_cleaned += deleted
        
        if deleted > 0:
            print_colored(f"  ✅ Cleaned {deleted} entries", Colors.GREEN)
        else:
            print_colored(f"  ℹ️  No Augment entries found", Colors.BLUE)
        print()
    
    print_colored("=" * 60, Colors.CYAN)
    print_colored(f"🎉 Database cleaning completed!", Colors.GREEN)
    print_colored(f"📈 Total entries cleaned: {total_cleaned}", Colors.GREEN)
    print_colored("=" * 60, Colors.CYAN)

def modify_telemetry_ids() -> None:
    """Modify VS Code telemetry IDs"""
    print_colored("🔧 Starting telemetry ID modification...", Colors.GREEN)
    print()
    
    storage_path = get_storage_json_path()
    
    if not storage_path:
        print_colored("⚠️  VS Code storage.json file not found", Colors.YELLOW)
        return
    
    try:
        # Create backup
        backup_path = create_backup(storage_path)
        print_colored(f"📁 Created backup: {backup_path.name}", Colors.BLUE)
        
        # Read current storage.json
        with open(storage_path, 'r', encoding='utf-8') as f:
            storage_data = json.load(f)
        
        # Generate new IDs
        new_machine_id = secrets.token_hex(32)  # 64-character hex string
        new_device_id = str(uuid.uuid4())  # UUID v4
        
        # Update IDs
        old_machine_id = storage_data.get('telemetry.machineId', 'Not found')
        old_device_id = storage_data.get('telemetry.devDeviceId', 'Not found')
        
        storage_data['telemetry.machineId'] = new_machine_id
        storage_data['telemetry.devDeviceId'] = new_device_id
        
        # Write updated storage.json
        with open(storage_path, 'w', encoding='utf-8') as f:
            json.dump(storage_data, f, indent=2)
        
        print_colored("🔄 Telemetry IDs updated:", Colors.CYAN)
        print_colored(f"  📱 Machine ID: {old_machine_id[:16]}... → {new_machine_id[:16]}...", Colors.BLUE)
        print_colored(f"  🖥️  Device ID: {old_device_id} → {new_device_id}", Colors.BLUE)
        print()
        print_colored("✅ Telemetry ID modification completed!", Colors.GREEN)
        
    except Exception as e:
        print_colored(f"❌ Error modifying telemetry IDs: {e}", Colors.RED)

def main():
    """Main function"""
    print_header()
    
    if len(sys.argv) < 2:
        print_colored("Usage:", Colors.YELLOW)
        print_colored("  python augment_cleaner.py clean          # Clean databases", Colors.WHITE)
        print_colored("  python augment_cleaner.py modify-ids     # Modify telemetry IDs", Colors.WHITE)
        print_colored("  python augment_cleaner.py all            # Run both operations", Colors.WHITE)
        return
    
    command = sys.argv[1].lower()
    
    if command == "clean":
        clean_databases()
    elif command == "modify-ids":
        modify_telemetry_ids()
    elif command == "all":
        clean_databases()
        print()
        modify_telemetry_ids()
    else:
        print_colored(f"❌ Unknown command: {command}", Colors.RED)
        print_colored("Available commands: clean, modify-ids, all", Colors.YELLOW)

if __name__ == "__main__":
    main()
